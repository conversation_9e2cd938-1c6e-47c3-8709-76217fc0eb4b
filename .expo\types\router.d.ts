/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/helpers/service`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/components/AppFooter`; params?: Router.UnknownInputParams; } | { pathname: `/components/AppHeader`; params?: Router.UnknownInputParams; } | { pathname: `/components/AppLayout`; params?: Router.UnknownInputParams; } | { pathname: `/components/auth-popup`; params?: Router.UnknownInputParams; } | { pathname: `/components/authentication`; params?: Router.UnknownInputParams; } | { pathname: `/components/chatbox`; params?: Router.UnknownInputParams; } | { pathname: `/components/gateway-auth`; params?: Router.UnknownInputParams; } | { pathname: `/components/messages`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/appStateContext`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/authContext`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/breadcrumbUtils`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/mock`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/model`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/stylesheet`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/voice-recognition`; params?: Router.UnknownInputParams; } | { pathname: `/loaders/fontLoader`; params?: Router.UnknownInputParams; } | { pathname: `/mock/mock`; params?: Router.UnknownInputParams; } | { pathname: `/modals/options`; params?: Router.UnknownInputParams; } | { pathname: `/modals/popup`; params?: Router.UnknownInputParams; } | { pathname: `/modals/sidebar`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/app-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/appLayout.test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/authentication-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/breadcrumbUtils.test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/chatbox-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/expo-router-navigation.test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/sidebar-test`; params?: Router.UnknownInputParams; } | { pathname: `/paas-source/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/helpers/service`; params?: Router.UnknownOutputParams; } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `/components/AppFooter`; params?: Router.UnknownOutputParams; } | { pathname: `/components/AppHeader`; params?: Router.UnknownOutputParams; } | { pathname: `/components/AppLayout`; params?: Router.UnknownOutputParams; } | { pathname: `/components/auth-popup`; params?: Router.UnknownOutputParams; } | { pathname: `/components/authentication`; params?: Router.UnknownOutputParams; } | { pathname: `/components/chatbox`; params?: Router.UnknownOutputParams; } | { pathname: `/components/gateway-auth`; params?: Router.UnknownOutputParams; } | { pathname: `/components/messages`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/appStateContext`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/authContext`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/breadcrumbUtils`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/mock`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/model`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/stylesheet`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/voice-recognition`; params?: Router.UnknownOutputParams; } | { pathname: `/loaders/fontLoader`; params?: Router.UnknownOutputParams; } | { pathname: `/mock/mock`; params?: Router.UnknownOutputParams; } | { pathname: `/modals/options`; params?: Router.UnknownOutputParams; } | { pathname: `/modals/popup`; params?: Router.UnknownOutputParams; } | { pathname: `/modals/sidebar`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/app-test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/appLayout.test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/authentication-test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/breadcrumbUtils.test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/chatbox-test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/expo-router-navigation.test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/sidebar-test`; params?: Router.UnknownOutputParams; } | { pathname: `/paas-source/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/helpers/service${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `/components/AppFooter${`?${string}` | `#${string}` | ''}` | `/components/AppHeader${`?${string}` | `#${string}` | ''}` | `/components/AppLayout${`?${string}` | `#${string}` | ''}` | `/components/auth-popup${`?${string}` | `#${string}` | ''}` | `/components/authentication${`?${string}` | `#${string}` | ''}` | `/components/chatbox${`?${string}` | `#${string}` | ''}` | `/components/gateway-auth${`?${string}` | `#${string}` | ''}` | `/components/messages${`?${string}` | `#${string}` | ''}` | `/helpers/appStateContext${`?${string}` | `#${string}` | ''}` | `/helpers/authContext${`?${string}` | `#${string}` | ''}` | `/helpers/breadcrumbUtils${`?${string}` | `#${string}` | ''}` | `/helpers/mock${`?${string}` | `#${string}` | ''}` | `/helpers/model${`?${string}` | `#${string}` | ''}` | `/helpers/stylesheet${`?${string}` | `#${string}` | ''}` | `/helpers/voice-recognition${`?${string}` | `#${string}` | ''}` | `/loaders/fontLoader${`?${string}` | `#${string}` | ''}` | `/mock/mock${`?${string}` | `#${string}` | ''}` | `/modals/options${`?${string}` | `#${string}` | ''}` | `/modals/popup${`?${string}` | `#${string}` | ''}` | `/modals/sidebar${`?${string}` | `#${string}` | ''}` | `/__tests__/app-test${`?${string}` | `#${string}` | ''}` | `/__tests__/appLayout.test${`?${string}` | `#${string}` | ''}` | `/__tests__/authentication-test${`?${string}` | `#${string}` | ''}` | `/__tests__/breadcrumbUtils.test${`?${string}` | `#${string}` | ''}` | `/__tests__/chatbox-test${`?${string}` | `#${string}` | ''}` | `/__tests__/expo-router-navigation.test${`?${string}` | `#${string}` | ''}` | `/__tests__/sidebar-test${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/helpers/service`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/components/AppFooter`; params?: Router.UnknownInputParams; } | { pathname: `/components/AppHeader`; params?: Router.UnknownInputParams; } | { pathname: `/components/AppLayout`; params?: Router.UnknownInputParams; } | { pathname: `/components/auth-popup`; params?: Router.UnknownInputParams; } | { pathname: `/components/authentication`; params?: Router.UnknownInputParams; } | { pathname: `/components/chatbox`; params?: Router.UnknownInputParams; } | { pathname: `/components/gateway-auth`; params?: Router.UnknownInputParams; } | { pathname: `/components/messages`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/appStateContext`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/authContext`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/breadcrumbUtils`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/mock`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/model`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/stylesheet`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/voice-recognition`; params?: Router.UnknownInputParams; } | { pathname: `/loaders/fontLoader`; params?: Router.UnknownInputParams; } | { pathname: `/mock/mock`; params?: Router.UnknownInputParams; } | { pathname: `/modals/options`; params?: Router.UnknownInputParams; } | { pathname: `/modals/popup`; params?: Router.UnknownInputParams; } | { pathname: `/modals/sidebar`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/app-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/appLayout.test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/authentication-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/breadcrumbUtils.test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/chatbox-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/expo-router-navigation.test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/sidebar-test`; params?: Router.UnknownInputParams; } | `/paas-source/${Router.SingleRoutePart<T>}` | { pathname: `/paas-source/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
