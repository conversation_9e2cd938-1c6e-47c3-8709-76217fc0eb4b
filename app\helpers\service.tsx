import { ApiUserData, ChatResponse, Conversation, SitecoreData, UserData } from "./model";

const API_BASE_URL = process.env.EXPO_PUBLIC_CONVERSATION_API_URL;  
const GRAPHQL_API_URL= process.env.EXPO_PUBLIC_GRAPHQL_URL || '';

// GET: Retrieve user's conversation history
export const getUserConversations = async (
  userId: string,
  accessToken: string,
): Promise<ApiUserData[]> => {
  const API_ENDPOINT= process.env.EXPO_PUBLIC_GENAI_USER_CONVERSATION_ENDPOINT || '';
  const params = `?userId=${userId}`;
  const url = `${API_BASE_URL}${API_ENDPOINT}${params}`;
  console.log('Fetching user conversations from:', url);
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        Authorization: `Bearer ${accessToken}`
      }
    });
    const data: ApiUserData[] = await response.json();
    return data;
    
  } catch (error) {
    console.error('Error fetching user data:', error);
    throw error;
  }
};

// POST: Send message to AI model
export const postMessage = async (
  userId: string,
  accessToken: string,
  message: string,
  conversationId: string,
): Promise<ChatResponse> => {
  const API_ENDPOINT= process.env.EXPO_PUBLIC_GENAI_CHAT_ENDPOINT || '';
  const url = API_BASE_URL + API_ENDPOINT;
  try {
    const response = await fetch(url, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
            question: `${message}`,
            userId: userId,
            conversationId: `${conversationId}`,
        }),
      });
    const data: ChatResponse = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
};

// POST: Rename conversation
export const postRename = async (
  userId: string,
  accessToken: string,  
  conversationId: string,
  name: string,
) => {
  const API_ENDPOINT= process.env.EXPO_PUBLIC_GENAI_RENAME_ENDPOINT || '';
  const url = API_BASE_URL + API_ENDPOINT;
  fetch(url, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
          userId: userId,
          conversationId: `${conversationId}`,
          name: `${name}`,
      }),
    })
    .then(response => response.json())
    .then(data => data)
    .catch(error => {
      console.error('Error fetching data:', error);
    });
};

// POST: Rate message and include optional feedback
export const postRate = async (
  userId: string,
  accessToken: string, 
  conversationId: string,
  msgIndex: number,
  rating: number,
  comment: string,
) => {
  const API_ENDPOINT= process.env.EXPO_PUBLIC_GENAI_RATE_ENDPOINT || '';
  const url = API_BASE_URL + API_ENDPOINT;
  fetch(url, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
          userId: userId,
          conversationId: `${conversationId}`,
          msgIndex: `${msgIndex}`,
          rating: `${rating}`,
          comment: `${comment}`,
      }),
    })
    .then(response => response.json())
    .then(data => data)
    .catch(error => {
      console.error('Error fetching data:', error);
    });
};

// POST: Check latest agreement version
export const checkAgreementHistory = async (
  userId: string,
  accessToken: string,  
  termsOfAgreementText: string,
): Promise<boolean> => {
  const API_ENDPOINT= process.env.EXPO_PUBLIC_GENAI_CHECK_AGREEMENT_ENDPOINT || '';
  const url = API_BASE_URL + API_ENDPOINT;
  try {
    const response = await fetch(url, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
            userId: userId,
            latestAgreement: `${termsOfAgreementText}`,
        }),
      });
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching data:', error);
    return false;
  }
};

// POST: Update latest agreement version
export const postAgreementHistory = async (
  userId: string,
  accessToken: string,
) => {
  const API_ENDPOINT= process.env.EXPO_PUBLIC_GENAI_POST_AGREEMENT_ENDPOINT || ''; //TODO
  const url = API_BASE_URL + API_ENDPOINT;
  fetch(url, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
          userId: userId,
      }),
    })
    .then(response => response.json())
    .then(data => data)
    .catch(error => {
      console.error('Error fetching data:', error);
    });
};

// DELETE: Delete conversation
export const deleteConversation = async (
  userId: string,
  accessToken: string,
  conversationId: string,
) => {
  const API_ENDPOINT= process.env.EXPO_PUBLIC_GENAI_DELETE_ENDPOINT || '';
  const url = API_BASE_URL + API_ENDPOINT;
  fetch(url, {
      method: 'DELETE',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
          userId: userId,
          conversationId: conversationId ? `${conversationId}` : `all`,
      }),
    })
    .then(response => response.json())
    .then(data => data)
    .catch(error => {
      console.error('Error fetching data:', error);
    });
};

// Return genAI data from sitecore
export const fetchSitecoreData = async (
): Promise<SitecoreData> => {
  const path = process.env.EXPO_PUBLIC_GRAPHQL_ENDPOINT || '';
  const url = `${GRAPHQL_API_URL}${path}?sc_apikey={${process.env.EXPO_PUBLIC_SITECORE_API_KEY}}`;
  const lang = process.env.EXPO_PUBLIC_DEFAULT_LANGUAGE || 'en';
  console.log('Fetching Sitecore data from:', url);
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
            query 
            {
              sitecore: item(path: "{D1E3D6B7-EF7B-423C-AB8F-0A5922834DEA}", language: "en") 
              {
                NewConversationText: field(name: "NewConversationText") {
                  value
                }
                  GreetingMessageText: field(name: "GreetingMessageText") {
                  value
                }
                  InputPlaceholderText: field(name: "InputPlaceholderText") {
                  value
                }
                  ConversationStartersText: field(name: "ConversationStartersText") {
                  value
                }
                  BotNameText: field(name: "BotNameText") {
                  value
                }
                HowToUseText: field(name: "HowToUseText") {
                  value
                }
                TermsOfUseText: field(name: "TermsOfUseText") {
                  value
                }	
              }
            }
        `,
      }),
    });    

    const res: any = await response.json();
    return res.data.sitecore;
    
  } catch (error) {
    console.error('Error fetching sitecore data:', error);
    throw error;
  }
};


// Default export
export default {
  getUserConversations,
  postMessage,
  postRename,
  postRate,
  checkAgreementHistory,
  postAgreementHistory,
  deleteConversation
};